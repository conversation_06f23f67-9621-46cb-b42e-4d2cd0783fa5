#!/bin/bash

# EAS Build Pre-install Script
# This script runs before the main build process to ensure all dependencies are properly installed

set -e

echo "🔧 Running pre-install setup..."

# Force install CocoaPods regardless of platform detection
echo "📦 Force installing CocoaPods..."

# Try multiple installation methods
if ! command -v pod &> /dev/null; then
    echo "📦 CocoaPods not found, installing..."

    # Method 1: Try gem install
    if gem install cocoapods --no-document; then
        echo "✅ CocoaPods installed via gem"
    else
        echo "⚠️ Gem install failed, trying alternative methods..."

        # Method 2: Try with sudo
        if sudo gem install cocoapods --no-document; then
            echo "✅ CocoaPods installed via sudo gem"
        else
            echo "⚠️ Sudo gem install failed"

            # Method 3: Try homebrew if available
            if command -v brew &> /dev/null; then
                echo "🍺 Trying Homebrew installation..."
                brew install cocoapods || echo "⚠️ Homebrew install failed"
            fi
        fi
    fi
else
    echo "✅ CocoaPods already installed: $(pod --version)"
fi

# Verify CocoaPods installation
if command -v pod &> /dev/null; then
    echo "✅ CocoaPods verification successful: $(pod --version)"

    # Setup CocoaPods
    echo "🔄 Setting up CocoaPods..."
    pod setup --silent || echo "⚠️ Pod setup failed, continuing..."

    # Update repo
    echo "🔄 Updating CocoaPods repo..."
    pod repo update --silent || echo "⚠️ CocoaPods repo update failed, continuing..."
else
    echo "❌ CocoaPods installation failed!"
    exit 1
fi

# Ensure Node.js version is correct
echo "🟢 Node.js version: $(node --version)"
echo "📦 npm version: $(npm --version)"

# Add CocoaPods to PATH if not already there
export PATH="/usr/local/bin:$PATH"
export PATH="$HOME/.gem/ruby/bin:$PATH"

echo "✅ Pre-install setup completed!"
