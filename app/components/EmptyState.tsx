import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

interface EmptyStateProps {
    icon: string;
    title: string;
    description: string;
}

const EmptyState = ({ icon, title, description }: EmptyStateProps) => {
    const { theme } = useTheme();

    return (
        <View style={styles.container}>
            <Ionicons name={icon as any} size={64} color={theme.disabled} />
            <Text style={[styles.title, { color: theme.textSecondary }]}>{title}</Text>
            <Text style={[styles.description, { color: theme.textTertiary }]}>{description}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 32,
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 16,
        marginBottom: 8,
    },
    description: {
        fontSize: 14,
        textAlign: 'center',
    },
});

export default EmptyState; 