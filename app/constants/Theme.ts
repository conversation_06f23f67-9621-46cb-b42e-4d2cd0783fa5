export interface ThemeColors {
  // 主要颜色
  primary: string;
  primaryLight: string;
  primaryDark: string;
  
  // 背景颜色
  background: string;
  surface: string;
  card: string;
  
  // 文本颜色
  text: string;
  textSecondary: string;
  textTertiary: string;
  
  // 功能颜色
  income: string;
  expense: string;
  success: string;
  warning: string;
  error: string;
  
  // 边框和分割线
  border: string;
  divider: string;
  
  // 状态颜色
  active: string;
  inactive: string;
  disabled: string;
  
  // 特殊颜色
  shadow: string;
  overlay: string;
  
  // 收入背景色
  incomeBackground: string;
  // 支出背景色
  expenseBackground: string;
}

export const lightTheme: ThemeColors = {
  // 主要颜色
  primary: '#dc4446',
  primaryLight: '#fff1f1',
  primaryDark: '#b83638',
  
  // 背景颜色
  background: '#f5f5f5',
  surface: '#ffffff',
  card: '#ffffff',
  
  // 文本颜色
  text: '#333333',
  textSecondary: '#666666',
  textTertiary: '#999999',
  
  // 功能颜色
  income: '#FF9A2E',
  expense: '#dc4446',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#dc4446',
  
  // 边框和分割线
  border: '#eeeeee',
  divider: '#eeeeee',
  
  // 状态颜色
  active: '#dc4446',
  inactive: '#666666',
  disabled: '#cccccc',
  
  // 特殊颜色
  shadow: '#000000',
  overlay: 'rgba(0, 0, 0, 0.5)',
  
  // 特殊背景色
  incomeBackground: '#FFF8E7',
  expenseBackground: '#FFF1F1',
};

export const darkTheme: ThemeColors = {
  // 主要颜色 - 与亮色主题完全相同
  primary: '#dc4446',
  primaryLight: '#fff1f1',
  primaryDark: '#b83638',

  // 背景颜色 - 与亮色主题完全相同
  background: '#f5f5f5',
  surface: '#ffffff',
  card: '#ffffff',

  // 文本颜色 - 与亮色主题完全相同
  text: '#333333',
  textSecondary: '#666666',
  textTertiary: '#999999',

  // 功能颜色 - 与亮色主题完全相同
  income: '#FF9A2E',
  expense: '#dc4446',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#dc4446',

  // 边框和分割线 - 与亮色主题完全相同
  border: '#eeeeee',
  divider: '#eeeeee',

  // 状态颜色 - 与亮色主题完全相同
  active: '#dc4446',
  inactive: '#666666',
  disabled: '#cccccc',

  // 特殊颜色 - 与亮色主题完全相同
  shadow: '#000000',
  overlay: 'rgba(0, 0, 0, 0.5)',

  // 特殊背景色 - 与亮色主题完全相同
  incomeBackground: '#FFF8E7',
  expenseBackground: '#FFF1F1',
};

export type ThemeMode = 'light' | 'dark';

export const themes = {
  light: lightTheme,
  dark: darkTheme,
};
