import React, { useState, useRef } from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Link, router } from 'expo-router';
import Stats from './components/Stats';
import HomeList from './components/HomeList';
import i18n from './i18n';
import Profile from './screens/profile';
import { useTheme } from './context/ThemeContext';

const App = () => {

  const [activeTab, setActiveTab] = useState('home');
  const { theme } = useTheme();
  const fadeAnim = useRef(new Animated.Value(1)).current;

  const handleTabChange = (newTab: string) => {
    if (newTab === activeTab) return;

    // 淡出动画
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start(() => {
      // 切换内容
      setActiveTab(newTab);
      // 淡入动画
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }).start();
    });
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'stats':
        return <Stats />;
      case 'profile':
        return <Profile />;
      case 'home':
        return <HomeList />;
      default:
        return <HomeList />;
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
        {renderContent()}
      </Animated.View>

      {/* 底部导航栏 */}
      <View style={[styles.navbar, { backgroundColor: theme.surface }]}>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() => handleTabChange('home')}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name="home"
            size={24}
            color={activeTab === 'home' ? theme.primary : theme.inactive}
          />
          <Text style={[
            activeTab === 'home' ? { color: theme.primary } : { color: theme.inactive },
            styles.navText
          ]}>
            {i18n.t('home.title')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() => handleTabChange('stats')}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name="stats-chart"
            size={24}
            color={activeTab === 'stats' ? theme.primary : theme.inactive}
          />
          <Text style={[
            activeTab === 'stats' ? { color: theme.primary } : { color: theme.inactive },
            styles.navText
          ]}>
            {i18n.t('home.stats')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.navItem}
          onPress={() => handleTabChange('profile')}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons
            name="person"
            size={24}
            color={activeTab === 'profile' ? theme.primary : theme.inactive}
          />
          <Text style={[
            activeTab === 'profile' ? { color: theme.primary } : { color: theme.inactive },
            styles.navText
          ]}>
            {i18n.t('home.profile')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* 浮动添加按钮 */}
      <TouchableOpacity
        style={[styles.floatingButton, { backgroundColor: theme.primary, shadowColor: theme.primary }]}
        onPress={() => router.push('/screens/add')}
      >
        <Text style={styles.floatingButtonText}>+</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 80,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
    marginTop: 40,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  buttons: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 24,
  },
  incomeButton: {
    flex: 1,
    backgroundColor: '#dc4446',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  expenseButton: {
    flex: 1,
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#dc4446',
  },
  incomeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  expenseButtonText: {
    color: '#dc4446',
    fontSize: 16,
    fontWeight: '500',
  },
  transactionSection: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  filters: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  activeFilter: {
    backgroundColor: '#fff1f1',
    padding: 4,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  activeFilterText: {
    color: '#dc4446',
  },
  filterText: {
    color: '#666',
  },
  transactionList: {
    flex: 1,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#fff1f1',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 20,
  },
  transactionInfo: {
    gap: 4,
  },
  transactionType: {
    fontWeight: '500',
  },
  transactionDate: {
    color: '#999',
    fontSize: 14,
  },
  transactionAmount: {
    fontWeight: '500',
    fontSize: 16,
  },
  navbar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 8,
    paddingHorizontal: 4,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 5,
  },
  navItem: {
    alignItems: 'center',
    gap: 4,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
  },
  navText: {
    fontSize: 12,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  floatingButtonText: {
    color: 'white',
    fontSize: 24,
  },
});

export default App;
