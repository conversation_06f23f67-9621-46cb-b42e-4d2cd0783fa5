import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface CrashReport {
  id: string;
  timestamp: number;
  error: {
    name: string;
    message: string;
    stack?: string;
  };
  context: {
    platform: string;
    version: string;
    buildNumber: string;
    userAgent?: string;
  };
  breadcrumbs: Breadcrumb[];
  deviceInfo: {
    platform: string;
    version: string;
    model?: string;
  };
}

interface Breadcrumb {
  timestamp: number;
  message: string;
  level: 'info' | 'warning' | 'error';
  category: string;
}

class CrashReporter {
  private breadcrumbs: Breadcrumb[] = [];
  private maxBreadcrumbs = 50;
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;

    try {
      // 设置全局错误处理器
      this.setupGlobalErrorHandlers();
      
      // 加载之前的面包屑
      await this.loadBreadcrumbs();
      
      this.isInitialized = true;
      this.addBreadcrumb('CrashReporter initialized', 'info', 'system');
      
      console.log('✅ CrashReporter initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize CrashReporter:', error);
    }
  }

  private setupGlobalErrorHandlers() {
    // 处理JavaScript错误
    const originalConsoleError = console.error;
    console.error = (...args) => {
      this.addBreadcrumb(`Console error: ${args.join(' ')}`, 'error', 'console');
      originalConsoleError.apply(console, args);
    };

    // 处理未捕获的Promise拒绝
    if (typeof global !== 'undefined' && global.addEventListener) {
      global.addEventListener('unhandledrejection', (event) => {
        this.handleUnhandledRejection(event.reason);
      });
    }

    // 处理React Native的错误
    if (global.ErrorUtils) {
      const originalGlobalHandler = global.ErrorUtils.getGlobalHandler();
      global.ErrorUtils.setGlobalHandler((error, isFatal) => {
        this.handleJavaScriptError(error, isFatal);
        if (originalGlobalHandler) {
          originalGlobalHandler(error, isFatal);
        }
      });
    }
  }

  private async handleJavaScriptError(error: Error, isFatal: boolean) {
    try {
      this.addBreadcrumb(
        `JavaScript error: ${error.message}`,
        'error',
        isFatal ? 'fatal' : 'error'
      );

      const crashReport: CrashReport = {
        id: this.generateId(),
        timestamp: Date.now(),
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        context: {
          platform: Platform.OS,
          version: '1.0.0', // 从app.json获取
          buildNumber: '24', // 从app.json获取
        },
        breadcrumbs: [...this.breadcrumbs],
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version.toString(),
        },
      };

      await this.saveCrashReport(crashReport);
      
      if (__DEV__) {
        console.error('🚨 Crash Report Generated:', crashReport);
      }
    } catch (reportError) {
      console.error('Failed to generate crash report:', reportError);
    }
  }

  private handleUnhandledRejection(reason: any) {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    this.addBreadcrumb(
      `Unhandled promise rejection: ${error.message}`,
      'error',
      'promise'
    );
    
    // 不要让未处理的Promise拒绝导致应用崩溃
    console.error('Unhandled promise rejection:', error);
  }

  addBreadcrumb(message: string, level: 'info' | 'warning' | 'error', category: string) {
    const breadcrumb: Breadcrumb = {
      timestamp: Date.now(),
      message,
      level,
      category,
    };

    this.breadcrumbs.push(breadcrumb);

    // 保持面包屑数量在限制内
    if (this.breadcrumbs.length > this.maxBreadcrumbs) {
      this.breadcrumbs = this.breadcrumbs.slice(-this.maxBreadcrumbs);
    }

    // 异步保存面包屑
    this.saveBreadcrumbs().catch(error => {
      console.warn('Failed to save breadcrumbs:', error);
    });
  }

  private async saveCrashReport(report: CrashReport) {
    try {
      const key = `crash_report_${report.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(report));
      
      // 保存崩溃报告列表
      const existingReports = await this.getCrashReportIds();
      existingReports.push(report.id);
      
      // 只保留最近的10个报告
      const recentReports = existingReports.slice(-10);
      await AsyncStorage.setItem('crash_report_ids', JSON.stringify(recentReports));
      
      // 清理旧的报告
      for (const oldId of existingReports.slice(0, -10)) {
        await AsyncStorage.removeItem(`crash_report_${oldId}`);
      }
    } catch (error) {
      console.error('Failed to save crash report:', error);
    }
  }

  private async saveBreadcrumbs() {
    try {
      await AsyncStorage.setItem('breadcrumbs', JSON.stringify(this.breadcrumbs));
    } catch (error) {
      console.warn('Failed to save breadcrumbs:', error);
    }
  }

  private async loadBreadcrumbs() {
    try {
      const stored = await AsyncStorage.getItem('breadcrumbs');
      if (stored) {
        this.breadcrumbs = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load breadcrumbs:', error);
      this.breadcrumbs = [];
    }
  }

  private async getCrashReportIds(): Promise<string[]> {
    try {
      const stored = await AsyncStorage.getItem('crash_report_ids');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Failed to get crash report IDs:', error);
      return [];
    }
  }

  async getCrashReports(): Promise<CrashReport[]> {
    try {
      const ids = await this.getCrashReportIds();
      const reports: CrashReport[] = [];
      
      for (const id of ids) {
        try {
          const stored = await AsyncStorage.getItem(`crash_report_${id}`);
          if (stored) {
            reports.push(JSON.parse(stored));
          }
        } catch (error) {
          console.warn(`Failed to load crash report ${id}:`, error);
        }
      }
      
      return reports.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Failed to get crash reports:', error);
      return [];
    }
  }

  async clearCrashReports() {
    try {
      const ids = await this.getCrashReportIds();
      
      for (const id of ids) {
        await AsyncStorage.removeItem(`crash_report_${id}`);
      }
      
      await AsyncStorage.removeItem('crash_report_ids');
      await AsyncStorage.removeItem('breadcrumbs');
      
      this.breadcrumbs = [];
      
      console.log('✅ Crash reports cleared');
    } catch (error) {
      console.error('Failed to clear crash reports:', error);
    }
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 手动报告错误
  reportError(error: Error, context?: any) {
    this.addBreadcrumb(
      `Manual error report: ${error.message}`,
      'error',
      'manual'
    );
    
    if (context) {
      this.addBreadcrumb(
        `Error context: ${JSON.stringify(context)}`,
        'info',
        'context'
      );
    }
    
    this.handleJavaScriptError(error, false);
  }

  // 记录用户操作
  logUserAction(action: string, details?: any) {
    const message = details 
      ? `User action: ${action} - ${JSON.stringify(details)}`
      : `User action: ${action}`;
    
    this.addBreadcrumb(message, 'info', 'user');
  }

  // 记录网络请求
  logNetworkRequest(url: string, method: string, status?: number) {
    const message = status 
      ? `Network: ${method} ${url} - ${status}`
      : `Network: ${method} ${url}`;
    
    this.addBreadcrumb(message, status && status >= 400 ? 'error' : 'info', 'network');
  }
}

// 创建全局实例
export const crashReporter = new CrashReporter();

// 自动初始化
crashReporter.initialize().catch(error => {
  console.error('Failed to auto-initialize crash reporter:', error);
});
