import { getLocales } from 'expo-localization';
import { I18n } from 'i18n-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 导入翻译文件
const zh = require('./translations/zh.json');
const en = require('./translations/en.json');

// 创建 i18n 实例
const i18n = new I18n({
    en,
    zh,
});

// 设置默认语言为英语
i18n.defaultLocale = 'zh';
i18n.enableFallback = true;

// 初始化语言设置
const initializeLanguage = async () => {
    try {
        // 首先尝试从存储中获取保存的语言设置
        const savedLanguage = await AsyncStorage.getItem('setting_language');
        if (savedLanguage && (savedLanguage === 'zh' || savedLanguage === 'en')) {
            i18n.locale = savedLanguage;
            return;
        }
    } catch (error) {
        console.log('Failed to load saved language:', error);
    }

    // 如果没有保存的语言设置，使用设备语言
    const locales = getLocales();
    if (locales.length > 0) {
        // 获取首选语言的简写代码（如 'zh' 或 'en'）
        const languageCode = locales[0].languageTag.split('-')[0];
        i18n.locale = languageCode;
    }
};

// 初始化语言
initializeLanguage();

// 添加切换语言的函数
export const changeLanguage = (languageCode: string) => {
    i18n.locale = languageCode;
};

export default i18n;