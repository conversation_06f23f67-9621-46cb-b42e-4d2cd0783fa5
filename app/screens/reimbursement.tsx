import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, Modal, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n from '../i18n';
import { getTransactions, updateTransaction } from '../constants/Storage';
import { useTransactionContext } from '../context/TransactionContext';
import { useSettings } from '../context/SettingsContext';
import { useTheme } from '../context/ThemeContext';
import EmptyState from '../components/EmptyState';

interface Transaction {
  id: number;
  type: 'income' | 'expense';
  amount: number;
  category: string;
  categoryIcon: string;
  note: string;
  date: string;
  member_id: number;
  refunded: boolean;
  refund_amount?: number;
  reimbursement_status?: 'none' | 'pending' | 'completed';
  tags?: number[];
  exclude_from_budget: boolean;
  shopping_platform: string;
}

const ReimbursementScreen = () => {
  const [pendingTransactions, setPendingTransactions] = useState<Transaction[]>([]);
  const [completedTransactions, setCompletedTransactions] = useState<Transaction[]>([]);
  const [activeTab, setActiveTab] = useState<'pending' | 'completed'>('pending');
  const [showReimbursementModal, setShowReimbursementModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [reimbursementAmount, setReimbursementAmount] = useState('');
  const { refreshTrigger, triggerRefresh } = useTransactionContext();
  const { currency } = useSettings();
  const { theme } = useTheme();

  // 加载待报销和已报销的交易
  const loadReimbursementTransactions = async () => {
    try {
      // 获取所有支出交易
      const { transactions: allTransactions } = await getTransactions({
        page: 1,
        pageSize: 1000, // 获取所有记录
        filter: 'expense'
      });

      // 将分组的交易转换为平面数组
      const flatTransactions: Transaction[] = [];
      Object.values(allTransactions).forEach(dayTransactions => {
        flatTransactions.push(...dayTransactions);
      });

      // 筛选待报销和已报销的交易
      const pending = flatTransactions.filter(t => t.reimbursement_status === 'pending');
      const completed = flatTransactions.filter(t => t.reimbursement_status === 'completed');

      setPendingTransactions(pending);
      setCompletedTransactions(completed);
    } catch (error) {
      console.error('Failed to load reimbursement transactions:', error);
    }
  };

  useEffect(() => {
    loadReimbursementTransactions();
  }, [refreshTrigger]);

  // 标记为已报销
  const handleMarkAsReimbursed = async (transaction: Transaction) => {
    try {
      await updateTransaction(transaction.id, {
        reimbursement_status: 'completed'
      });
      loadReimbursementTransactions();
      triggerRefresh();
    } catch (error) {
      console.error('Failed to mark as reimbursed:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('reimbursement.reimbursementFailed'));
    }
  };

  // 部分报销
  const handlePartialReimbursement = async () => {
    if (!selectedTransaction || !reimbursementAmount) {
      Alert.alert(i18n.t('common.error'), i18n.t('common.pleaseEnterAmount'));
      return;
    }

    const reimbursementAmountNum = parseFloat(reimbursementAmount);
    const originalAmount = Math.abs(selectedTransaction.amount);

    if (reimbursementAmountNum <= 0 || reimbursementAmountNum > originalAmount) {
      Alert.alert(
        i18n.t('common.error'),
        i18n.t('reimbursement.invalidReimbursementAmount', { max: originalAmount.toFixed(2) })
      );
      return;
    }

    try {
      const isFullyReimbursed = reimbursementAmountNum >= originalAmount;
      
      await updateTransaction(selectedTransaction.id, {
        reimbursement_status: isFullyReimbursed ? 'completed' : 'pending',
        // 这里可以添加部分报销金额字段，类似退款功能
      });

      setShowReimbursementModal(false);
      setSelectedTransaction(null);
      setReimbursementAmount('');
      loadReimbursementTransactions();
      triggerRefresh();
    } catch (error) {
      console.error('Failed to process reimbursement:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('reimbursement.reimbursementFailed'));
    }
  };

  // 渲染交易项
  const renderTransactionItem = (transaction: Transaction) => {
    return (
      <View key={transaction.id} style={[styles.transactionItem, { backgroundColor: theme.surface }]}>
        <View style={styles.transactionLeft}>
          <View style={[styles.categoryIcon, { backgroundColor: theme.expenseBackground }]}>
            <Text style={styles.iconText}>{transaction.categoryIcon}</Text>
          </View>
          <View style={styles.transactionInfo}>
            <Text style={[styles.transactionCategory, { color: theme.text }]}>
              {transaction.category}
            </Text>
            {transaction.note && (
              <Text style={[styles.transactionNote, { color: theme.textSecondary }]}>
                {transaction.note}
              </Text>
            )}
            <Text style={[styles.transactionDate, { color: theme.textTertiary }]}>
              {new Date(transaction.date).toLocaleDateString()}
            </Text>
          </View>
        </View>
        <View style={styles.transactionRight}>
          <Text style={[styles.transactionAmount, { color: theme.expense }]}>
            {currency}{Math.abs(transaction.amount).toFixed(2)}
          </Text>
          {activeTab === 'pending' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.actionButton, styles.partialButton]}
                onPress={() => {
                  setSelectedTransaction(transaction);
                  setReimbursementAmount('');
                  setShowReimbursementModal(true);
                }}
              >
                <Text style={styles.actionButtonText}>{i18n.t('reimbursement.partialReimbursement')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionButton, styles.completeButton]}
                onPress={() => handleMarkAsReimbursed(transaction)}
              >
                <Text style={styles.actionButtonText}>{i18n.t('reimbursement.markAsReimbursed')}</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  };

  // 渲染报销模态框
  const renderReimbursementModal = () => (
    <Modal
      visible={showReimbursementModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowReimbursementModal(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowReimbursementModal(false)}
      >
        <View style={styles.reimbursementModal} onStartShouldSetResponder={() => true}>
          <Text style={styles.reimbursementModalTitle}>{i18n.t('reimbursement.partialReimbursement')}</Text>

          {selectedTransaction && (
            <View style={styles.reimbursementInfo}>
              <Text style={styles.reimbursementInfoText}>
                {i18n.t('common.originalAmount')}: {currency}{Math.abs(selectedTransaction.amount).toFixed(2)}
              </Text>
            </View>
          )}

          <TextInput
            style={styles.reimbursementAmountInput}
            placeholder={i18n.t('reimbursement.enterReimbursementAmount')}
            value={reimbursementAmount}
            onChangeText={setReimbursementAmount}
            keyboardType="numeric"
            autoFocus
          />

          <View style={styles.reimbursementModalButtons}>
            <TouchableOpacity
              style={[styles.reimbursementModalButton, styles.reimbursementCancelButton]}
              onPress={() => setShowReimbursementModal(false)}
            >
              <Text style={styles.reimbursementCancelButtonText}>{i18n.t('common.cancel')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.reimbursementModalButton, styles.reimbursementConfirmButton]}
              onPress={handlePartialReimbursement}
            >
              <Text style={styles.reimbursementConfirmButtonText}>{i18n.t('common.confirm')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const currentTransactions = activeTab === 'pending' ? pendingTransactions : completedTransactions;

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.text }]}>{i18n.t('reimbursement.title')}</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Tab Selector */}
      <View style={[styles.tabContainer, { borderBottomColor: theme.border }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'pending' && { borderBottomColor: theme.primary }
          ]}
          onPress={() => setActiveTab('pending')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'pending' ? theme.primary : theme.textSecondary }
          ]}>
            {i18n.t('reimbursement.pendingReimbursement')} ({pendingTransactions.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'completed' && { borderBottomColor: theme.primary }
          ]}
          onPress={() => setActiveTab('completed')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'completed' ? theme.primary : theme.textSecondary }
          ]}>
            {i18n.t('reimbursement.reimbursed')} ({completedTransactions.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {currentTransactions.length === 0 ? (
          <EmptyState
            icon="receipt-outline"
            title={i18n.t('reimbursement.noReimbursementItems')}
            description={i18n.t('reimbursement.addReimbursementItemsDescription')}
          />
        ) : (
          currentTransactions.map(renderTransactionItem)
        )}
      </ScrollView>

      {renderReimbursementModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    marginHorizontal: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 44,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  iconText: {
    fontSize: 20,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionCategory: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  transactionNote: {
    fontSize: 14,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  partialButton: {
    backgroundColor: '#FF9800',
  },
  completeButton: {
    backgroundColor: '#4CAF50',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  reimbursementModal: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  reimbursementModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333',
  },
  reimbursementInfo: {
    marginBottom: 16,
  },
  reimbursementInfoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  reimbursementAmountInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  reimbursementModalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  reimbursementModalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  reimbursementCancelButton: {
    backgroundColor: '#f5f5f5',
  },
  reimbursementConfirmButton: {
    backgroundColor: '#dc4446',
  },
  reimbursementCancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  reimbursementConfirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ReimbursementScreen;
