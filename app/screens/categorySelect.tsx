import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert, TextInput, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { getCategories, addCategory, getFavorites, deleteFavorite, updateFavoriteOrder, getMembers, addTransaction, addFavorite } from '../constants/Storage';
import { Category } from './categories';
import i18n from '../i18n';
import { useTheme } from '../context/ThemeContext';
import { Swipeable } from 'react-native-gesture-handler';
import DraggableFlatList, { ScaleDecorator } from 'react-native-draggable-flatlist';
import Animated from 'react-native-reanimated';
import EmptyState from '../components/EmptyState';

// 定义收藏记录的类型
export interface FavoriteRecord {
  id: number;
  amount: number;
  category: string;
  categoryIcon: string;
  note: string;
  sort_order: number;
  member_id: number;
}

interface Member {
  id: number;
  name: string;
}

const CategorySelect = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [activeTab, setActiveTab] = useState<'expense' | 'income'>('expense');
  const [activeMode, setActiveMode] = useState<'new' | 'favorites'>('new');
  const [showCategoryImportModal, setShowCategoryImportModal] = useState(false);
  const [selectedImportCategories, setSelectedImportCategories] = useState<string[]>([]);
  const [favorites, setFavorites] = useState<FavoriteRecord[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const swipeableRefs = useRef<{ [key: number]: Swipeable | null }>({});

  // 快速记账状态
  const [showQuickAdd, setShowQuickAdd] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [quickAmount, setQuickAmount] = useState('');
  const [quickNote, setQuickNote] = useState('');
  const quickAmountInputRef = useRef<TextInput>(null);

  const params = useLocalSearchParams() as any;
  const { theme } = useTheme();

  useEffect(() => {
    if (params.type) {
      setActiveTab(params.type as 'expense' | 'income');
    }
    loadCategories();
    loadMembers();
  }, [params.type]);

  useEffect(() => {
    loadCategories();
    loadFavorites();
  }, [activeTab]);

  const loadCategories = async () => {
    try {
      const result = await getCategories(activeTab);
      setCategories(result);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const loadFavorites = async () => {
    try {
      const favs = await getFavorites(activeTab);
      setFavorites(favs);
    } catch (error) {
      console.error('Failed to load favorites:', error);
    }
  };

  const loadMembers = async () => {
    try {
      const data = await getMembers();
      setMembers(data);
    } catch (error) {
      console.error('Failed to load members:', error);
    }
  };

  const handleCategorySelect = (category: Category) => {
    // 显示快速记账界面
    setSelectedCategory(category);
    setShowQuickAdd(true);
    setQuickAmount('');
    setQuickNote('');

    // 延迟聚焦到金额输入框，确保界面已经渲染
    setTimeout(() => {
      quickAmountInputRef.current?.focus();
    }, 100);
  };

  const handleFavoriteSelect = (favorite: FavoriteRecord) => {
    // 导航到记账页面，传递收藏的信息
    router.push({
      pathname: '/screens/add',
      params: {
        type: activeTab,
        amount: favorite.amount.toString(),
        category: favorite.category,
        categoryIcon: favorite.categoryIcon,
        note: favorite.note,
        member_id: favorite.member_id.toString(),
        fromCategorySelect: 'true'
      }
    });
  };

  const handleDeleteFavorite = async (id: number) => {
    try {
      await deleteFavorite(activeTab, id);
      loadFavorites();
    } catch (error) {
      console.error('Failed to delete favorite:', error);
    }
  };

  const closeAllSwipeables = () => {
    Object.values(swipeableRefs.current).forEach(ref => {
      ref?.close();
    });
  };

  const handleQuickComplete = async () => {
    if (!selectedCategory || !quickAmount || parseFloat(quickAmount) <= 0) {
      Alert.alert(i18n.t('common.error'), i18n.t('add.validation.amountRequired'));
      return;
    }

    try {
      const transactionAmount = parseFloat(quickAmount);
      const finalAmount = activeTab === 'income' ?
        Math.abs(transactionAmount) :
        -Math.abs(transactionAmount);

      const formattedDate = new Date().toISOString().split('T')[0];

      await addTransaction({
        type: activeTab,
        amount: finalAmount,
        category: selectedCategory.name,
        categoryIcon: selectedCategory.icon,
        note: quickNote,
        date: formattedDate,
        member_id: 1,
        refunded: false,
        exclude_from_budget: false,
        tags: '',
        shopping_platform: ''
      });

      setShowQuickAdd(false);
      setSelectedCategory(null);
      setQuickAmount('');
      setQuickNote('');

      // 返回上一页
      router.back();
    } catch (error) {
      console.error('Failed to save transaction:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('add.saveFailed'));
    }
  };

  const handleQuickSaveToFavorites = async () => {
    if (!selectedCategory || !quickAmount || parseFloat(quickAmount) <= 0) {
      Alert.alert(i18n.t('common.error'), i18n.t('add.validation.amountRequired'));
      return;
    }

    try {
      await addFavorite({
        type: activeTab,
        amount: parseFloat(quickAmount),
        category: selectedCategory.name,
        categoryIcon: selectedCategory.icon,
        note: quickNote,
        member_id: 1,
      });

      loadFavorites();
      setShowQuickAdd(false);
      setSelectedCategory(null);
      setQuickAmount('');
      setQuickNote('');

      // 切换到收藏列表
      setActiveMode('favorites');
    } catch (error) {
      console.error('Failed to add to favorites:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('add.saveFailed'));
    }
  };

  const renderCategoryName = (name: string) => {
    // 允许显示最多5个汉字，不截断
    if (name.length <= 5) return name;
    return name.slice(0, 5) + '...';
  };

  // 推荐分类数据
  const getRecommendedCategories = () => {
    if (activeTab === 'expense') {
      return [
        { name: i18n.t('categories.food'), icon: '🍽️' },
        { name: i18n.t('categories.transport'), icon: '🚗' },
        { name: i18n.t('categories.shopping'), icon: '🛒' },
        { name: i18n.t('categories.entertainment'), icon: '🎬' },
        { name: i18n.t('categories.healthcare'), icon: '🏥' },
        { name: i18n.t('categories.education'), icon: '📚' },
        { name: i18n.t('categories.housing'), icon: '🏠' },
        { name: i18n.t('categories.utilities'), icon: '💡' },
      ];
    } else {
      return [
        { name: i18n.t('categories.salary'), icon: '💰' },
        { name: i18n.t('categories.bonus'), icon: '🎁' },
        { name: i18n.t('categories.investment'), icon: '📈' },
        { name: i18n.t('categories.freelance'), icon: '💻' },
        { name: i18n.t('categories.gift'), icon: '🎉' },
        { name: i18n.t('categories.other'), icon: '📝' },
      ];
    }
  };

  const handleImportCategories = async () => {
    if (selectedImportCategories.length === 0) {
      Alert.alert(i18n.t('common.error'), i18n.t('add.selectCategoriesToImport'));
      return;
    }

    try {
      const recommendedCategories = getRecommendedCategories();
      
      for (const categoryName of selectedImportCategories) {
        const categoryData = recommendedCategories.find(cat => cat.name === categoryName);
        if (categoryData) {
          await addCategory({
            type: activeTab,
            name: categoryData.name,
            icon: categoryData.icon,
          });
        }
      }

      setSelectedImportCategories([]);
      setShowCategoryImportModal(false);
      loadCategories();
      
      Alert.alert(
        '',
        i18n.t('add.categoriesImported'),
        [{ text: i18n.t('common.ok') }]
      );
    } catch (error) {
      console.error('Failed to import categories:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('add.importFailed'));
    }
  };

  const renderCategoryGrid = () => (
    <ScrollView
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.categoryGrid}>
        {categories.length === 0 ? (
          // 当没有分类时显示导入推荐分类的选项
          <TouchableOpacity
            style={[styles.categoryItem, styles.importCategoryItem]}
            onPress={() => setShowCategoryImportModal(true)}
          >
            <Ionicons name="download-outline" size={32} color="#4CAF50" />
            <Text style={[styles.categoryName, { color: '#4CAF50' }]}>
              {i18n.t('add.oneClickImport')}
            </Text>
          </TouchableOpacity>
        ) : (
          categories.map(category => (
            <TouchableOpacity
              key={category.id}
              style={styles.categoryItem}
              onPress={() => handleCategorySelect(category)}
            >
              <Text style={styles.categoryIcon}>{category.icon}</Text>
              <Text
                style={[styles.categoryName, { color: theme.text }]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {renderCategoryName(category.name)}
              </Text>
            </TouchableOpacity>
          ))
        )}

        {/* 添加新分类按钮 */}
        <TouchableOpacity
          style={[styles.categoryItem, styles.addCategoryItem]}
          onPress={() => router.push({
            pathname: '/screens/categories',
            params: { initialTab: activeTab }
          })}
        >
          <Ionicons name="add" size={32} color="#dc4446" />
          <Text style={[styles.categoryName, { color: '#dc4446' }]}>
            {i18n.t('common.add')}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderFavoritesList = () => (
    <DraggableFlatList
      data={favorites}
      onDragEnd={async ({ data }) => {
        setFavorites(data);
        await updateFavoriteOrder(
          activeTab,
          data.map((item, index) => ({
            id: item.id,
            sort_order: index
          }))
        );
      }}
      keyExtractor={item => item.id.toString()}
      renderItem={({ item, drag, isActive }) => (
        <ScaleDecorator>
          <Animated.View>
            <Swipeable
              ref={(ref) => {
                swipeableRefs.current[item.id] = ref;
              }}
              renderRightActions={() => (
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => handleDeleteFavorite(item.id)}
                >
                  <Ionicons name="trash-outline" size={24} color="white" />
                </TouchableOpacity>
              )}
            >
              <TouchableOpacity
                style={[
                  styles.favoriteItem,
                  isActive && styles.activeFavoriteItem,
                  { backgroundColor: theme.surface }
                ]}
                onPress={() => handleFavoriteSelect(item)}
                onLongPress={drag}
                delayLongPress={150}
              >
                <View style={styles.favoriteItemLeft}>
                  <View style={[
                    styles.favoriteCategoryIcon,
                    { backgroundColor: activeTab === 'income' ? '#FFF8E7' : '#FFF1F1' }
                  ]}>
                    <Text style={styles.favoriteCategoryIconText}>
                      {item.categoryIcon}
                    </Text>
                  </View>
                  <View style={styles.favoriteItemInfo}>
                    <Text style={[styles.favoriteItemCategory, { color: theme.text }]}>{item.category}</Text>
                    {item.note && (
                      <Text style={[styles.favoriteItemNote, { color: theme.textSecondary }]} numberOfLines={1}>
                        {item.note}
                      </Text>
                    )}
                    {item.member_id > 0 && (
                      <Text style={[styles.favoriteItemMember, { color: theme.textSecondary }]}>
                        {members.find(m => m.id === item.member_id)?.name || ''}
                      </Text>
                    )}
                  </View>
                </View>
                <View style={styles.favoriteItemRight}>
                  <Text style={[
                    styles.favoriteItemAmount,
                    { color: activeTab === 'income' ? '#FF9A2E' : '#dc4446' }
                  ]}>
                    {activeTab === 'income' ? '+' : '-'}¥{item.amount.toFixed(2)}
                  </Text>
                  <Ionicons
                    name="menu"
                    size={18}
                    color="#999"
                    style={styles.dragHandle}
                  />
                </View>
              </TouchableOpacity>
            </Swipeable>
          </Animated.View>
        </ScaleDecorator>
      )}
      contentContainerStyle={styles.favoritesList}
      style={styles.favoritesContainer}
      ListEmptyComponent={
        <EmptyState
          icon="star-outline"
          title={i18n.t('add.noFavorites')}
          description={i18n.t('add.clickAddButtonToCreate')}
        />
      }
    />
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]} onTouchStart={closeAllSwipeables}>
      {/* 标题栏 */}
      <View style={[styles.header, { backgroundColor: theme.surface }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>
          {i18n.t('add.selectCategory')}
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* 收入支出切换 */}
      <View style={[styles.tabs, { backgroundColor: theme.surface }]}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'expense' && styles.activeTab]}
          onPress={() => setActiveTab('expense')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'expense' && styles.activeTabText
          ]}>{i18n.t('add.addExpense')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'income' && styles.activeTab]}
          onPress={() => setActiveTab('income')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'income' && styles.activeTabText
          ]}>{i18n.t('add.addIncome')}</Text>
        </TouchableOpacity>
      </View>

      {/* 创建模式切换 */}
      <View style={[styles.modeTabs, { backgroundColor: theme.surface }]}>
        <TouchableOpacity
          style={[styles.modeTab, activeMode === 'new' && styles.activeModeTab]}
          onPress={() => setActiveMode('new')}
        >
          <Text style={[
            styles.modeTabText,
            activeMode === 'new' && styles.activeModeTabText
          ]}>{i18n.t('add.new')}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.modeTab, activeMode === 'favorites' && styles.activeModeTab]}
          onPress={() => setActiveMode('favorites')}
        >
          <Text style={[
            styles.modeTabText,
            activeMode === 'favorites' && styles.activeModeTabText
          ]}>{i18n.t('add.favorites')}</Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      {activeMode === 'new' ? renderCategoryGrid() : renderFavoritesList()}

      {/* 快速记账模态框 */}
      {showQuickAdd && selectedCategory && (
        <Modal
          visible={showQuickAdd}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowQuickAdd(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.quickAddModalContent, { backgroundColor: theme.surface }]}>
              <View style={styles.quickAddHeader}>
                <TouchableOpacity
                  style={styles.quickAddCloseButton}
                  onPress={() => setShowQuickAdd(false)}
                >
                  <Ionicons name="close" size={24} color={theme.text} />
                </TouchableOpacity>
                <Text style={[styles.quickAddTitle, { color: theme.text }]}>
                  {activeTab === 'income' ? i18n.t('add.addIncome') : i18n.t('add.addExpense')}
                </Text>
                <View style={styles.placeholder} />
              </View>

              <View style={styles.selectedCategoryDisplay}>
                <Text style={styles.categoryIcon}>{selectedCategory.icon}</Text>
                <Text style={[styles.selectedCategoryName, { color: theme.text }]}>
                  {selectedCategory.name}
                </Text>
              </View>

              <View style={styles.quickAddInputContainer}>
                <Text style={[styles.currencySymbol, { color: theme.text }]}>¥</Text>
                <TextInput
                  ref={quickAmountInputRef}
                  style={[styles.quickAddAmountInput, { color: theme.text }]}
                  value={quickAmount}
                  onChangeText={setQuickAmount}
                  keyboardType="decimal-pad"
                  placeholder="0.00"
                  placeholderTextColor="#999"
                  autoFocus={true}
                />
              </View>

              <TextInput
                style={[styles.quickAddNoteInput, { color: theme.text, backgroundColor: theme.background }]}
                value={quickNote}
                onChangeText={setQuickNote}
                placeholder={i18n.t('common.note')}
                placeholderTextColor="#999"
                multiline
                numberOfLines={2}
              />

              <View style={styles.quickAddButtons}>
                <TouchableOpacity
                  style={[styles.quickAddButton, styles.quickAddFavoriteButton]}
                  onPress={handleQuickSaveToFavorites}
                >
                  <Ionicons name="star-outline" size={20} color="#dc4446" />
                  <Text style={styles.quickAddFavoriteButtonText}>{i18n.t('add.addToFavorites')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.quickAddButton, styles.quickAddCompleteButton]}
                  onPress={handleQuickComplete}
                >
                  <Text style={styles.quickAddCompleteButtonText}>{i18n.t('common.save')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {/* 分类导入模态框 */}
      {showCategoryImportModal && (
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
            <Text style={[styles.modalTitle, { color: theme.text }]}>
              {i18n.t('add.oneClickImport')}
            </Text>
            <ScrollView style={styles.modalScrollView}>
              {getRecommendedCategories().map((category, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.importCategoryOption,
                    selectedImportCategories.includes(category.name) && styles.selectedImportCategory
                  ]}
                  onPress={() => {
                    setSelectedImportCategories(prev =>
                      prev.includes(category.name)
                        ? prev.filter(name => name !== category.name)
                        : [...prev, category.name]
                    );
                  }}
                >
                  <Text style={styles.importCategoryIcon}>{category.icon}</Text>
                  <Text style={[styles.importCategoryName, { color: theme.text }]}>
                    {category.name}
                  </Text>
                  {selectedImportCategories.includes(category.name) && (
                    <Ionicons name="checkmark" size={20} color="#4CAF50" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowCategoryImportModal(false);
                  setSelectedImportCategories([]);
                }}
              >
                <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleImportCategories}
              >
                <Text style={styles.confirmButtonText}>{i18n.t('common.confirm')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  tabs: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: '#dc4446',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: 'white',
    fontWeight: '500',
  },
  modeTabs: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 12,
    padding: 4,
  },
  modeTab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeModeTab: {
    backgroundColor: '#fff1f1',
  },
  modeTabText: {
    fontSize: 14,
    color: '#666',
  },
  activeModeTabText: {
    color: '#dc4446',
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  categoryItem: {
    width: '30%',
    aspectRatio: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  importCategoryItem: {
    backgroundColor: '#f0f9f0',
    borderWidth: 1,
    borderColor: '#4CAF50',
    borderStyle: 'dashed',
  },
  addCategoryItem: {
    backgroundColor: '#fff1f1',
    borderWidth: 1,
    borderColor: '#dc4446',
    borderStyle: 'dashed',
  },
  categoryIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 11,
    textAlign: 'center',
    fontWeight: '500',
    lineHeight: 14,
    minHeight: 28, // 确保有足够空间显示两行文字
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalScrollView: {
    maxHeight: 300,
  },
  importCategoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedImportCategory: {
    backgroundColor: '#f0f9f0',
  },
  importCategoryIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  importCategoryName: {
    flex: 1,
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  confirmButton: {
    backgroundColor: '#dc4446',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  favoritesContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  favoritesList: {
    paddingBottom: 100,
  },
  favoriteItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  activeFavoriteItem: {
    opacity: 0.8,
  },
  favoriteItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  favoriteCategoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  favoriteCategoryIconText: {
    fontSize: 20,
  },
  favoriteItemInfo: {
    flex: 1,
  },
  favoriteItemCategory: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  favoriteItemNote: {
    fontSize: 14,
    marginBottom: 2,
  },
  favoriteItemMember: {
    fontSize: 12,
  },
  favoriteItemRight: {
    alignItems: 'flex-end',
  },
  favoriteItemAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  dragHandle: {
    marginLeft: 8,
  },
  deleteButton: {
    backgroundColor: '#dc4446',
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
    marginBottom: 12,
    borderRadius: 12,
  },
  quickAddModalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 20,
    maxHeight: '70%',
  },
  quickAddHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  quickAddCloseButton: {
    padding: 4,
  },
  quickAddTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  selectedCategoryDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
  },
  selectedCategoryName: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  quickAddInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
  },
  currencySymbol: {
    fontSize: 24,
    fontWeight: '600',
    marginRight: 8,
  },
  quickAddAmountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
  },
  quickAddNoteInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  quickAddButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  quickAddButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    gap: 8,
  },
  quickAddFavoriteButton: {
    backgroundColor: '#fff1f1',
  },
  quickAddFavoriteButtonText: {
    color: '#dc4446',
    fontSize: 16,
    fontWeight: '500',
  },
  quickAddCompleteButton: {
    backgroundColor: '#dc4446',
  },
  quickAddCompleteButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default CategorySelect;
