import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    FlatList,
    Alert,
    Modal,
    TextInput,
    Platform,
    ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSettings } from '../context/SettingsContext';
import { useTheme } from '../context/ThemeContext';
import i18n from '../i18n';
import {
    LendingRecord,
    LendingRepayment,
    getLendingRecords,
    addLendingRepayment,
    getLendingRepayments,
} from '../constants/Storage';

export default function LendingDetailScreen() {
    const router = useRouter();
    const { recordId } = useLocalSearchParams();
    const { currency } = useSettings();
    const { theme } = useTheme();
    
    const [record, setRecord] = useState<LendingRecord | null>(null);
    const [repayments, setRepayments] = useState<LendingRepayment[]>([]);
    const [showRepaymentModal, setShowRepaymentModal] = useState(false);
    
    // Repayment form states
    const [repaymentAmount, setRepaymentAmount] = useState('');
    const [repaymentDate, setRepaymentDate] = useState(new Date());
    const [repaymentNote, setRepaymentNote] = useState('');
    const [showRepaymentDatePicker, setShowRepaymentDatePicker] = useState(false);

    useEffect(() => {
        if (recordId) {
            loadRecord();
            loadRepayments();
        }
    }, [recordId]);

    const loadRecord = async () => {
        try {
            const records = await getLendingRecords();
            const foundRecord = records.find(r => r.id.toString() === recordId);
            setRecord(foundRecord || null);
        } catch (error) {
            console.error('Failed to load lending record:', error);
        }
    };

    const loadRepayments = async () => {
        try {
            if (recordId) {
                const repaymentList = await getLendingRepayments(parseInt(recordId as string));
                setRepayments(repaymentList);
            }
        } catch (error) {
            console.error('Failed to load repayments:', error);
        }
    };

    const handleAddRepayment = async () => {
        if (!record) return;

        const repaymentAmountValue = parseFloat(repaymentAmount);
        if (!repaymentAmount.trim() || isNaN(repaymentAmountValue) || repaymentAmountValue <= 0) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.invalidRepaymentAmount'));
            return;
        }

        try {
            await addLendingRepayment({
                lending_id: record.id,
                amount: repaymentAmountValue,
                repayment_date: repaymentDate.toISOString().split('T')[0],
                note: repaymentNote.trim(),
            });

            setRepaymentAmount('');
            setRepaymentNote('');
            setRepaymentDate(new Date());
            setShowRepaymentModal(false);
            await loadRepayments();
        } catch (error) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.addRepaymentFailed'));
        }
    };

    const calculateTotalRepaid = () => {
        return repayments.reduce((total, repayment) => total + repayment.amount, 0);
    };

    const calculateRemainingAmount = () => {
        if (!record) return 0;
        return record.amount - calculateTotalRepaid();
    };

    const renderRepayment = ({ item }: { item: LendingRepayment }) => (
        <View style={[styles.repaymentCard, { backgroundColor: theme.card, borderColor: theme.border }]}>
            <View style={styles.repaymentHeader}>
                <Text style={[styles.repaymentAmount, { color: theme.text }]}>
                    {currency}{item.amount.toFixed(2)}
                </Text>
                <Text style={[styles.repaymentDate, { color: theme.textSecondary }]}>
                    {item.repayment_date}
                </Text>
            </View>
            {item.note && (
                <Text style={[styles.repaymentNote, { color: theme.textSecondary }]}>
                    {item.note}
                </Text>
            )}
        </View>
    );

    if (!record) {
        return (
            <View style={[styles.container, { backgroundColor: theme.background }]}>
                <View style={styles.emptyContainer}>
                    <Text style={[styles.emptyText, { color: theme.textSecondary }]}>{i18n.t('lending.recordNotFound')}</Text>
                </View>
            </View>
        );
    }

    const totalRepaid = calculateTotalRepaid();
    const remainingAmount = calculateRemainingAmount();
    const isFullyRepaid = remainingAmount <= 0;

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {!isFullyRepaid && (
                    <TouchableOpacity
                        style={[styles.addButton, { backgroundColor: theme.primary }]}
                        onPress={() => setShowRepaymentModal(true)}
                    >
                        <Ionicons name="add" size={24} color="white" />
                        <Text style={styles.addButtonText}>{i18n.t('lending.addRepayment')}</Text>
                    </TouchableOpacity>
                )}
                {/* Record Summary */}
                <View style={[styles.summaryCard, { backgroundColor: theme.card, borderColor: theme.border }]}>
                    <View style={styles.summaryHeader}>
                        <Text style={[styles.recordType, {
                            color: record.type === 'lend' ? '#4CAF50' : '#FF9800',
                            backgroundColor: record.type === 'lend' ? '#E8F5E8' : '#FFF3E0'
                        }]}>
                            {i18n.t(`lending.${record.type}`)}
                        </Text>
                        <Text style={[styles.personName, { color: theme.text }]}>{record.person_name}</Text>
                    </View>
                    
                    <Text style={[styles.originalAmount, { color: theme.text }]}>
                        {i18n.t('lending.originalAmount')}: {currency}{record.amount.toFixed(2)}
                    </Text>
                    
                    <Text style={[styles.repaidAmount, { color: '#4CAF50' }]}>
                        {i18n.t('lending.totalRepaid')}: {currency}{totalRepaid.toFixed(2)}
                    </Text>
                    
                    <Text style={[styles.remainingAmount, { 
                        color: isFullyRepaid ? '#4CAF50' : '#FF9800' 
                    }]}>
                        {i18n.t('lending.remainingAmount')}: {currency}{remainingAmount.toFixed(2)}
                    </Text>

                    <Text style={[styles.recordDate, { color: theme.textSecondary }]}>
                        {i18n.t('lending.lendingDate')}: {record.lending_date}
                    </Text>
                    
                    {record.due_date && (
                        <Text style={[styles.recordDate, { color: theme.textSecondary }]}>
                            {i18n.t('lending.dueDate')}: {record.due_date}
                        </Text>
                    )}
                    
                    {record.interest_rate > 0 && (
                        <Text style={[styles.recordDate, { color: theme.textSecondary }]}>
                            {i18n.t('lending.interestRate')}: {record.interest_rate}%
                        </Text>
                    )}
                    
                    {record.note && (
                        <Text style={[styles.recordNote, { color: theme.textSecondary }]}>
                            {i18n.t('lending.note')}: {record.note}
                        </Text>
                    )}
                </View>

                {/* Repayment History */}
                <View style={styles.repaymentSection}>
                    <Text style={[styles.sectionTitle, { color: theme.text }]}>
                        {i18n.t('lending.repaymentHistory')} ({repayments.length})
                    </Text>
                    
                    {repayments.length === 0 ? (
                        <View style={styles.emptyRepayments}>
                            <Text style={[styles.emptyRepaymentsText, { color: theme.textSecondary }]}>
                                {i18n.t('lending.noRepayments')}
                            </Text>
                        </View>
                    ) : (
                        <FlatList
                            data={repayments}
                            renderItem={renderRepayment}
                            keyExtractor={(item) => item.id.toString()}
                            scrollEnabled={false}
                        />
                    )}
                </View>
            </ScrollView>

            {/* Add Repayment Modal */}
            <Modal
                visible={showRepaymentModal}
                animationType="slide"
                presentationStyle="pageSheet"
                onRequestClose={() => setShowRepaymentModal(false)}
            >
                <View style={[styles.modalContainer, { backgroundColor: theme.background }]}>
                    <View style={[styles.modalHeader, { backgroundColor: theme.card, borderBottomColor: theme.border }]}>
                        <TouchableOpacity onPress={() => setShowRepaymentModal(false)}>
                            <Text style={[styles.modalCancelButton, { color: theme.primary }]}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                        <Text style={[styles.modalTitle, { color: theme.text }]}>
                            {i18n.t('lending.addRepayment')}
                        </Text>
                        <TouchableOpacity onPress={handleAddRepayment}>
                            <Text style={[styles.modalSaveButton, { color: theme.primary }]}>{i18n.t('common.save')}</Text>
                        </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.repaymentAmount')}</Text>
                        <TextInput
                            style={[styles.textInput, { backgroundColor: theme.card, borderColor: theme.border, color: theme.text }]}
                            placeholder={i18n.t('lending.enterRepaymentAmount')}
                            placeholderTextColor={theme.textTertiary}
                            value={repaymentAmount}
                            onChangeText={setRepaymentAmount}
                            keyboardType="decimal-pad"
                            returnKeyType="done"
                        />

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.repaymentDate')}</Text>
                        <TouchableOpacity
                            style={[styles.datePickerButton, { backgroundColor: theme.card, borderColor: theme.border }]}
                            onPress={() => setShowRepaymentDatePicker(true)}
                        >
                            <Text style={[styles.datePickerText, { color: theme.text }]}>
                                {repaymentDate.toISOString().split('T')[0]}
                            </Text>
                            <Ionicons name="calendar-outline" size={20} color="#666" />
                        </TouchableOpacity>

                        {showRepaymentDatePicker && (
                            <DateTimePicker
                                value={repaymentDate}
                                mode="date"
                                display={Platform.OS === 'ios' ? 'inline' : 'default'}
                                onChange={(_, selectedDate) => {
                                    setShowRepaymentDatePicker(false);
                                    if (selectedDate) {
                                        setRepaymentDate(selectedDate);
                                    }
                                }}
                                maximumDate={new Date()}
                                textColor="#333"
                                accentColor="#E91E63"
                                themeVariant="light"
                                locale={i18n.locale === 'zh' ? 'zh-CN' : 'en-US'}
                            />
                        )}

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.note')} {i18n.t('lending.optional')}</Text>
                        <TextInput
                            style={[styles.textInput, styles.noteInput, { backgroundColor: theme.card, borderColor: theme.border, color: theme.text }]}
                            placeholder={i18n.t('lending.enterNote')}
                            placeholderTextColor={theme.textTertiary}
                            value={repaymentNote}
                            onChangeText={setRepaymentNote}
                            multiline
                            numberOfLines={3}
                        />
                    </ScrollView>
                </View>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 8,
        marginBottom: 16,
        gap: 8,
    },
    addButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
    },
    content: {
        flex: 1,
        padding: 16,
    },
    summaryCard: {
        padding: 16,
        borderRadius: 12,
        marginBottom: 24,
        borderWidth: 1,
    },
    summaryHeader: {
        marginBottom: 12,
    },
    recordType: {
        fontSize: 12,
        fontWeight: '600',
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 12,
        alignSelf: 'flex-start',
        marginBottom: 4,
    },
    personName: {
        fontSize: 20,
        fontWeight: '700',
    },
    originalAmount: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    repaidAmount: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    remainingAmount: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
    },
    recordDate: {
        fontSize: 14,
        marginBottom: 2,
    },
    recordNote: {
        fontSize: 14,
        marginTop: 8,
        fontStyle: 'italic',
    },
    repaymentSection: {
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 12,
    },
    emptyRepayments: {
        padding: 24,
        alignItems: 'center',
    },
    emptyRepaymentsText: {
        fontSize: 16,
        textAlign: 'center',
    },
    repaymentCard: {
        padding: 12,
        borderRadius: 8,
        marginBottom: 8,
        borderWidth: 1,
    },
    repaymentHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    repaymentAmount: {
        fontSize: 16,
        fontWeight: '600',
    },
    repaymentDate: {
        fontSize: 14,
    },
    repaymentNote: {
        fontSize: 14,
        marginTop: 4,
        fontStyle: 'italic',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 32,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: '500',
        textAlign: 'center',
    },
    modalContainer: {
        flex: 1,
    },
    modalHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    modalCancelButton: {
        fontSize: 16,
        fontWeight: '500',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 16,
    },
    modalSaveButton: {
        fontSize: 16,
        fontWeight: '600',
    },
    modalBody: {
        flex: 1,
        padding: 16,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 8,
        marginTop: 16,
    },
    textInput: {
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
    },
    noteInput: {
        height: 80,
        textAlignVertical: 'top',
    },
    datePickerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
    },
    datePickerText: {
        fontSize: 16,
    },
});
