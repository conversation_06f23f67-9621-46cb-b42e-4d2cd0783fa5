import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    FlatList,
    Alert,
    Modal,
    TextInput,
    Platform,
    ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useRouter } from 'expo-router';
import { useSettings } from '../context/SettingsContext';
import { useTheme } from '../context/ThemeContext';
import i18n from '../i18n';
import {
    LendingRecord,
    LendingRepayment,
    addLendingRecord,
    getLendingRecords,
    updateLendingRecord,
    deleteLendingRecord,
    addLendingRepayment,
    getLendingRepayments,
} from '../constants/Storage';

export default function PersonalLendingScreen() {
    const router = useRouter();
    const { currency } = useSettings();
    const { theme } = useTheme();
    const [lendingRecords, setLendingRecords] = useState<LendingRecord[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showRepaymentModal, setShowRepaymentModal] = useState(false);
    const [editingRecord, setEditingRecord] = useState<LendingRecord | null>(null);
    const [selectedRecord, setSelectedRecord] = useState<LendingRecord | null>(null);

    // Form states
    const [recordType, setRecordType] = useState<'lend' | 'borrow'>('lend');
    const [personName, setPersonName] = useState('');
    const [amount, setAmount] = useState('');
    const [interestRate, setInterestRate] = useState('');
    const [lendingDate, setLendingDate] = useState(new Date());
    const [dueDate, setDueDate] = useState<Date | null>(null);
    const [note, setNote] = useState('');
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [showDueDatePicker, setShowDueDatePicker] = useState(false);

    // Repayment form states
    const [repaymentAmount, setRepaymentAmount] = useState('');
    const [repaymentDate, setRepaymentDate] = useState(new Date());
    const [repaymentNote, setRepaymentNote] = useState('');
    const [showRepaymentDatePicker, setShowRepaymentDatePicker] = useState(false);

    useEffect(() => {
        loadLendingRecords();
    }, []);

    const loadLendingRecords = async () => {
        try {
            const records = await getLendingRecords();
            setLendingRecords(records);
        } catch (error) {
            console.error('Failed to load lending records:', error);
        }
    };

    const handleAddRecord = async () => {
        if (!personName.trim()) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.personNameRequired'));
            return;
        }

        const amountValue = parseFloat(amount);
        if (!amount.trim() || isNaN(amountValue) || amountValue <= 0) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.invalidAmount'));
            return;
        }

        const interestRateValue = interestRate.trim() ? parseFloat(interestRate) : 0;
        if (interestRate.trim() && (isNaN(interestRateValue) || interestRateValue < 0)) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.invalidInterestRate'));
            return;
        }

        try {
            if (editingRecord) {
                await updateLendingRecord(editingRecord.id, {
                    person_name: personName.trim(),
                    amount: amountValue,
                    interest_rate: interestRateValue,
                    lending_date: lendingDate.toISOString().split('T')[0],
                    due_date: dueDate ? dueDate.toISOString().split('T')[0] : undefined,
                    note: note.trim(),
                });
            } else {
                await addLendingRecord({
                    type: recordType,
                    person_name: personName.trim(),
                    amount: amountValue,
                    interest_rate: interestRateValue,
                    lending_date: lendingDate.toISOString().split('T')[0],
                    due_date: dueDate ? dueDate.toISOString().split('T')[0] : undefined,
                    note: note.trim(),
                });
            }

            await loadLendingRecords();
            resetForm();
            setShowAddModal(false);
        } catch (error) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.addRecordFailed'));
        }
    };

    const handleEditRecord = (record: LendingRecord) => {
        setEditingRecord(record);
        setRecordType(record.type);
        setPersonName(record.person_name);
        setAmount(record.amount.toString());
        setInterestRate(record.interest_rate.toString());
        setLendingDate(new Date(record.lending_date));
        setDueDate(record.due_date ? new Date(record.due_date) : null);
        setNote(record.note || '');
        setShowAddModal(true);
    };

    const handleDeleteRecord = (record: LendingRecord) => {
        Alert.alert(
            i18n.t('lending.confirmDelete'),
            i18n.t('lending.confirmDeleteMessage'),
            [
                { text: i18n.t('common.cancel'), style: 'cancel' },
                {
                    text: i18n.t('common.delete'),
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await deleteLendingRecord(record.id);
                            await loadLendingRecords();
                        } catch (error) {
                            Alert.alert(i18n.t('common.error'), i18n.t('lending.deleteRecordFailed'));
                        }
                    },
                },
            ]
        );
    };

    const handleAddRepayment = async () => {
        if (!selectedRecord) return;

        const repaymentAmountValue = parseFloat(repaymentAmount);
        if (!repaymentAmount.trim() || isNaN(repaymentAmountValue) || repaymentAmountValue <= 0) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.invalidRepaymentAmount'));
            return;
        }

        try {
            await addLendingRepayment({
                lending_id: selectedRecord.id,
                amount: repaymentAmountValue,
                repayment_date: repaymentDate.toISOString().split('T')[0],
                note: repaymentNote.trim(),
            });

            setRepaymentAmount('');
            setRepaymentNote('');
            setRepaymentDate(new Date());
            setShowRepaymentModal(false);
            setSelectedRecord(null);
        } catch (error) {
            Alert.alert(i18n.t('common.error'), i18n.t('lending.addRepaymentFailed'));
        }
    };

    const resetForm = () => {
        setRecordType('lend');
        setPersonName('');
        setAmount('');
        setInterestRate('');
        setLendingDate(new Date());
        setDueDate(null);
        setNote('');
        setEditingRecord(null);
    };

    const renderRecord = ({ item }: { item: LendingRecord }) => (
        <TouchableOpacity
            style={[styles.recordCard, { backgroundColor: theme.card, borderColor: theme.border }]}
            onPress={() => {
                router.push({
                    pathname: '/screens/lendingDetail',
                    params: { recordId: item.id.toString() }
                });
            }}
        >
            <View style={styles.recordHeader}>
                <View style={styles.recordInfo}>
                    <Text style={[styles.recordType, {
                        color: item.type === 'lend' ? '#4CAF50' : '#FF9800',
                        backgroundColor: item.type === 'lend' ? '#E8F5E8' : '#FFF3E0'
                    }]}>
                        {i18n.t(`lending.${item.type}`)}
                    </Text>
                    <Text style={[styles.personName, { color: theme.text }]}>{item.person_name}</Text>
                </View>
                <View style={styles.recordActions}>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleEditRecord(item)}
                    >
                        <Ionicons name="pencil" size={16} color="#666" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleDeleteRecord(item)}
                    >
                        <Ionicons name="trash" size={16} color="#FF5252" />
                    </TouchableOpacity>
                </View>
            </View>
            <Text style={[styles.recordAmount, { color: theme.text }]}>
                {currency}{item.amount.toFixed(2)}
            </Text>
            <Text style={[styles.recordDate, { color: theme.textSecondary }]}>
                {i18n.t('lending.lendingDate')}: {item.lending_date}
            </Text>
            {item.due_date && (
                <Text style={[styles.recordDate, { color: theme.textSecondary }]}>
                    {i18n.t('lending.dueDate')}: {item.due_date}
                </Text>
            )}
            {item.interest_rate > 0 && (
                <Text style={[styles.recordDate, { color: theme.textSecondary }]}>
                    {i18n.t('lending.interestRate')}: {item.interest_rate}%
                </Text>
            )}
        </TouchableOpacity>
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            <View style={styles.contentContainer}>
                <TouchableOpacity
                    style={[styles.addButton, { backgroundColor: theme.primary }]}
                    onPress={() => setShowAddModal(true)}
                >
                    <Ionicons name="add" size={24} color="white" />
                    <Text style={styles.addButtonText}>{i18n.t('lending.addRecord')}</Text>
                </TouchableOpacity>

                {lendingRecords.length === 0 ? (
                <View style={styles.emptyContainer}>
                    <Ionicons name="document-text-outline" size={64} color="#ccc" />
                    <Text style={[styles.emptyText, { color: theme.textSecondary }]}>{i18n.t('lending.noRecords')}</Text>
                    <Text style={[styles.emptySubtext, { color: theme.textTertiary }]}>{i18n.t('lending.addFirstRecord')}</Text>
                </View>
            ) : (
                <FlatList
                    data={lendingRecords}
                    renderItem={renderRecord}
                    keyExtractor={(item) => item.id.toString()}
                    contentContainerStyle={styles.listContainer}
                    showsVerticalScrollIndicator={false}
                />
            )}
            </View>

            {/* Add/Edit Record Modal */}
            <Modal
                visible={showAddModal}
                animationType="slide"
                presentationStyle="pageSheet"
                onRequestClose={() => {
                    resetForm();
                    setShowAddModal(false);
                }}
            >
                <View style={[styles.modalContainer, { backgroundColor: theme.background }]}>
                    <View style={[styles.modalHeader, { backgroundColor: theme.card, borderBottomColor: theme.border }]}>
                        <TouchableOpacity
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                        >
                            <Text style={[styles.modalCancelButton, { color: theme.primary }]}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                        <Text style={[styles.modalTitle, { color: theme.text }]}>
                            {editingRecord ? i18n.t('lending.editRecord') : i18n.t('lending.addRecord')}
                        </Text>
                        <TouchableOpacity onPress={handleAddRecord}>
                            <Text style={[styles.modalSaveButton, { color: theme.primary }]}>{i18n.t('common.save')}</Text>
                        </TouchableOpacity>
                    </View>

                    <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.recordType')}</Text>
                        <View style={styles.recordTypeSelector}>
                            <TouchableOpacity
                                style={[styles.recordTypeOption, recordType === 'lend' && styles.selectedRecordType]}
                                onPress={() => setRecordType('lend')}
                            >
                                <Text style={[styles.recordTypeText, recordType === 'lend' && styles.selectedRecordTypeText]}>
                                    {i18n.t('lending.lend')}
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.recordTypeOption, recordType === 'borrow' && styles.selectedRecordType]}
                                onPress={() => setRecordType('borrow')}
                            >
                                <Text style={[styles.recordTypeText, recordType === 'borrow' && styles.selectedRecordTypeText]}>
                                    {i18n.t('lending.borrow')}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.personName')}</Text>
                        <TextInput
                            style={[styles.textInput, { backgroundColor: theme.card, borderColor: theme.border, color: theme.text }]}
                            placeholder={i18n.t('lending.personNameRequired')}
                            placeholderTextColor={theme.textTertiary}
                            value={personName}
                            onChangeText={setPersonName}
                        />

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.amount')}</Text>
                        <TextInput
                            style={[styles.textInput, { backgroundColor: theme.card, borderColor: theme.border, color: theme.text }]}
                            placeholder={i18n.t('lending.invalidAmount')}
                            placeholderTextColor={theme.textTertiary}
                            value={amount}
                            onChangeText={setAmount}
                            keyboardType="decimal-pad"
                            returnKeyType="done"
                        />

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.interestRate')} {i18n.t('lending.optional')}</Text>
                        <TextInput
                            style={[styles.textInput, { backgroundColor: theme.card, borderColor: theme.border, color: theme.text }]}
                            placeholder={i18n.t('lending.enterInterestRate')}
                            placeholderTextColor={theme.textTertiary}
                            value={interestRate}
                            onChangeText={setInterestRate}
                            keyboardType="decimal-pad"
                            returnKeyType="done"
                        />

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.lendingDate')}</Text>
                        <TouchableOpacity
                            style={[styles.datePickerButton, { backgroundColor: theme.card, borderColor: theme.border }]}
                            onPress={() => setShowDatePicker(true)}
                        >
                            <Text style={[styles.datePickerText, { color: theme.text }]}>
                                {lendingDate.toISOString().split('T')[0]}
                            </Text>
                            <Ionicons name="calendar-outline" size={20} color="#666" />
                        </TouchableOpacity>

                        {showDatePicker && (
                            <DateTimePicker
                                value={lendingDate}
                                mode="date"
                                display={Platform.OS === 'ios' ? 'inline' : 'default'}
                                onChange={(_, selectedDate) => {
                                    setShowDatePicker(false);
                                    if (selectedDate) {
                                        setLendingDate(selectedDate);
                                    }
                                }}
                                maximumDate={new Date()}
                                textColor="#333"
                                accentColor="#E91E63"
                                themeVariant="light"
                                locale={i18n.locale === 'zh' ? 'zh-CN' : 'en-US'}
                            />
                        )}

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.dueDate')} {i18n.t('lending.optional')}</Text>
                        <TouchableOpacity
                            style={[styles.datePickerButton, { backgroundColor: theme.card, borderColor: theme.border }]}
                            onPress={() => setShowDueDatePicker(true)}
                        >
                            <Text style={[styles.datePickerText, { color: theme.text }]}>
                                {dueDate ? dueDate.toISOString().split('T')[0] : i18n.t('lending.selectDueDate')}
                            </Text>
                            <Ionicons name="calendar-outline" size={20} color="#666" />
                        </TouchableOpacity>

                        {showDueDatePicker && (
                            <DateTimePicker
                                value={dueDate || new Date()}
                                mode="date"
                                display={Platform.OS === 'ios' ? 'inline' : 'default'}
                                onChange={(_, selectedDate) => {
                                    setShowDueDatePicker(false);
                                    if (selectedDate) {
                                        setDueDate(selectedDate);
                                    }
                                }}
                                minimumDate={new Date()}
                                textColor="#333"
                                accentColor="#E91E63"
                                themeVariant="light"
                                locale={i18n.locale === 'zh' ? 'zh-CN' : 'en-US'}
                            />
                        )}

                        {dueDate && (
                            <TouchableOpacity
                                style={styles.clearDateButton}
                                onPress={() => setDueDate(null)}
                            >
                                <Text style={styles.clearDateText}>{i18n.t('lending.clearDueDate')}</Text>
                            </TouchableOpacity>
                        )}

                        <Text style={[styles.inputLabel, { color: theme.text }]}>{i18n.t('lending.note')} {i18n.t('lending.optional')}</Text>
                        <TextInput
                            style={[styles.textInput, styles.noteInput, { backgroundColor: theme.card, borderColor: theme.border, color: theme.text }]}
                            placeholder={i18n.t('lending.enterNote')}
                            placeholderTextColor={theme.textTertiary}
                            value={note}
                            onChangeText={setNote}
                            multiline
                            numberOfLines={3}
                        />
                    </ScrollView>
                </View>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    contentContainer: {
        flex: 1,
        padding: 16,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 8,
        marginBottom: 16,
        gap: 8,
    },
    addButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 32,
    },
    emptyText: {
        fontSize: 18,
        fontWeight: '500',
        marginTop: 16,
        textAlign: 'center',
    },
    emptySubtext: {
        fontSize: 14,
        marginTop: 8,
        textAlign: 'center',
    },
    listContainer: {
        // padding: 16,
    },
    recordCard: {
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
        borderWidth: 1,
    },
    recordHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    recordInfo: {
        flex: 1,
    },
    recordType: {
        fontSize: 12,
        fontWeight: '600',
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 12,
        alignSelf: 'flex-start',
        marginBottom: 4,
    },
    personName: {
        fontSize: 16,
        fontWeight: '600',
    },
    recordActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        padding: 8,
    },
    recordAmount: {
        fontSize: 18,
        fontWeight: '700',
        marginBottom: 4,
    },
    recordDate: {
        fontSize: 14,
        marginBottom: 2,
    },
    modalContainer: {
        flex: 1,
    },
    modalHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    modalCancelButton: {
        fontSize: 16,
        fontWeight: '500',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        flex: 1,
        textAlign: 'center',
        marginHorizontal: 16,
    },
    modalSaveButton: {
        fontSize: 16,
        fontWeight: '600',
    },
    modalBody: {
        flex: 1,
        padding: 16,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 8,
        marginTop: 16,
    },
    textInput: {
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
        fontSize: 16,
    },
    noteInput: {
        height: 80,
        textAlignVertical: 'top',
    },
    recordTypeSelector: {
        flexDirection: 'row',
        gap: 8,
        marginTop: 8,
    },
    recordTypeOption: {
        flex: 1,
        paddingVertical: 10,
        paddingHorizontal: 12,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: '#ddd',
        alignItems: 'center',
        backgroundColor: '#f9f9f9',
    },
    selectedRecordType: {
        borderColor: '#E91E63',
        backgroundColor: '#fce4ec',
    },
    recordTypeText: {
        fontSize: 14,
        fontWeight: '500',
        color: '#666',
    },
    selectedRecordTypeText: {
        color: '#E91E63',
        fontWeight: '600',
    },
    datePickerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 10,
    },
    datePickerText: {
        fontSize: 16,
    },
    clearDateButton: {
        marginTop: 8,
        paddingVertical: 8,
        paddingHorizontal: 12,
        backgroundColor: '#f0f0f0',
        borderRadius: 6,
        alignItems: 'center',
    },
    clearDateText: {
        fontSize: 14,
        color: '#666',
    },
});
