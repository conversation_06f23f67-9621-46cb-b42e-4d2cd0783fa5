import React, { createContext, useContext, useState, useEffect } from 'react';
import { Appearance } from 'react-native';
import { ThemeColors, ThemeMode, themes } from '../constants/Theme';
import { getSetting, saveSetting } from '../constants/Storage';

interface ThemeContextType {
  theme: ThemeColors;
  themeMode: ThemeMode;
  isDark: boolean;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
}

const ThemeContext = createContext<ThemeContextType>({
  theme: themes.light,
  themeMode: 'light',
  isDark: false,
  toggleTheme: () => {},
  setThemeMode: () => {},
});

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [themeMode, setThemeModeState] = useState<ThemeMode>('light');

  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await getSetting('themeMode');
        if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
          setThemeModeState(savedTheme as ThemeMode);
        } else {
          // 如果没有保存的主题，使用系统主题
          const systemTheme = Appearance.getColorScheme();
          setThemeModeState(systemTheme === 'dark' ? 'dark' : 'light');
        }
      } catch (error) {
        console.error('Failed to load theme:', error);
        setThemeModeState('light');
      }
    };

    loadTheme();

    // 监听系统主题变化
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      // 只有在没有手动设置主题时才跟随系统
      getSetting('themeMode').then(savedTheme => {
        if (!savedTheme) {
          setThemeModeState(colorScheme === 'dark' ? 'dark' : 'light');
        }
      });
    });

    return () => subscription?.remove();
  }, []);

  const setThemeMode = async (mode: ThemeMode) => {
    try {
      await saveSetting('themeMode', mode);
      setThemeModeState(mode);
    } catch (error) {
      console.error('Failed to save theme:', error);
    }
  };

  const toggleTheme = () => {
    const newMode = themeMode === 'light' ? 'dark' : 'light';
    setThemeMode(newMode);
  };

  const theme = themes[themeMode];
  const isDark = themeMode === 'dark';

  return (
    <ThemeContext.Provider 
      value={{ 
        theme, 
        themeMode, 
        isDark, 
        toggleTheme, 
        setThemeMode 
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
