// Polyfill for ReadableStream and other Web APIs
if (typeof globalThis.ReadableStream === 'undefined') {
  const { ReadableStream, WritableStream, TransformStream } = require('web-streams-polyfill');
  globalThis.ReadableStream = ReadableStream;
  globalThis.WritableStream = WritableStream;
  globalThis.TransformStream = TransformStream;
}

// Additional polyfills for other missing Web APIs
if (typeof globalThis.TextEncoder === 'undefined') {
  const { TextEncoder, TextDecoder } = require('util');
  globalThis.TextEncoder = TextEncoder;
  globalThis.TextDecoder = TextDecoder;
}

if (typeof globalThis.fetch === 'undefined') {
  globalThis.fetch = require('node-fetch');
}

// Polyfill for os.availableParallelism (Node.js 18.14.0+)
const os = require('os');
if (typeof os.availableParallelism === 'undefined') {
  os.availableParallelism = () => os.cpus().length;
}
