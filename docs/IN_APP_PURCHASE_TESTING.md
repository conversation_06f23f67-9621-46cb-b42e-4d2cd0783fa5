# In-App Purchase 测试指南

本文档详细说明如何在不同环境中测试 In-App Purchase 功能。

## 🔍 环境说明

### Expo Go 环境
- **限制**: 某些原生模块（如 expo-in-app-purchases）在 Expo Go 中不可用
- **表现**: 会显示 "Cannot find native module 'ExpoInAppPurchases'" 错误
- **解决方案**: 应用会自动使用模拟实现，功能正常但不会产生真实购买

### 开发构建环境
- **优势**: 包含所有原生模块，功能完整
- **要求**: 需要构建自定义的开发客户端
- **适用**: 真实的 In-App Purchase 测试

### 生产环境
- **要求**: 必须使用开发构建或生产构建
- **配置**: 需要在应用商店中配置产品

## 🧪 测试策略

### 1. Expo Go 中的测试

在 Expo Go 中，应用会自动检测原生模块不可用并使用模拟实现：

```typescript
// 自动检测和降级
if (!isInAppPurchasesAvailable) {
  console.warn('In-App Purchases not available - using mock implementation');
  return true; // 模拟成功
}
```

**测试内容**:
- ✅ UI 流程正常
- ✅ 购买按钮可点击
- ✅ 模态框正常显示
- ✅ 错误处理正常
- ❌ 不会产生真实购买

### 2. 开发构建中的测试

#### 创建开发构建

```bash
# iOS
npx eas build --profile development --platform ios

# Android  
npx eas build --profile development --platform android
```

#### 安装和运行

```bash
# 安装构建到设备后
npx expo start --dev-client
```

**测试内容**:
- ✅ 真实的应用商店连接
- ✅ 产品信息获取
- ✅ 沙盒购买流程
- ✅ 购买历史恢复
- ✅ 错误处理

### 3. 沙盒测试配置

#### iOS 沙盒测试

1. **App Store Connect 配置**:
   - 创建产品: `com.yourcompany.ninecents.premium`
   - 设置为"准备提交"状态
   - 创建沙盒测试账户

2. **设备配置**:
   - 设置 > App Store > 沙盒账户
   - 登录测试账户

3. **测试流程**:
   ```bash
   # 使用开发构建
   npx expo start --dev-client
   ```

#### Android 沙盒测试

1. **Google Play Console 配置**:
   - 创建应用内产品
   - 设置测试轨道
   - 添加测试用户

2. **测试流程**:
   - 使用内部测试版本
   - 测试账户购买

## 🔧 调试工具

### 1. 测试函数

使用内置的测试函数检查配置：

```typescript
import { runInAppPurchaseTest } from './app/utils/testInAppPurchase';

// 在开发环境中调用
runInAppPurchaseTest();
```

### 2. 控制台日志

关键日志信息：

```
🧪 Testing In-App Purchase Setup...
✅ In-App Purchase Available: true/false
✅ Connected to App Store: true/false
✅ Products loaded: 1
📦 Product Details: {...}
```

### 3. 错误诊断

常见错误和解决方案：

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| `Cannot find native module` | Expo Go 环境 | 使用开发构建或接受模拟实现 |
| `Products loaded: 0` | 产品未配置 | 检查应用商店产品配置 |
| `Connected to App Store: false` | 网络或配置问题 | 检查网络和Bundle ID |
| `Purchase failed` | 沙盒账户问题 | 检查测试账户配置 |

## 📱 测试检查清单

### 开发阶段
- [ ] Expo Go 中 UI 流程正常
- [ ] 模拟购买功能正常
- [ ] 错误处理正确
- [ ] 国际化文本正确

### 集成测试阶段
- [ ] 开发构建成功创建
- [ ] 原生模块正常加载
- [ ] 应用商店连接成功
- [ ] 产品信息获取正常

### 沙盒测试阶段
- [ ] 沙盒账户配置正确
- [ ] 产品购买流程正常
- [ ] 购买历史恢复正常
- [ ] 错误场景处理正确

### 生产准备阶段
- [ ] 生产构建测试通过
- [ ] 应用商店审核准备
- [ ] 产品价格和描述正确
- [ ] 用户协议和隐私政策更新

## 🚀 发布建议

1. **渐进式发布**: 先在小范围用户中测试
2. **监控指标**: 关注购买成功率和错误率
3. **用户反馈**: 收集用户购买体验反馈
4. **回滚准备**: 准备快速回滚方案

## 📞 支持资源

- [Expo In-App Purchases 文档](https://docs.expo.dev/versions/latest/sdk/in-app-purchases/)
- [Apple 沙盒测试指南](https://developer.apple.com/documentation/storekit/in-app_purchase/testing_in-app_purchases_with_sandbox)
- [Google Play 测试指南](https://developer.android.com/google/play/billing/test)

## 💡 最佳实践

1. **优雅降级**: 在不支持的环境中提供替代方案
2. **详细日志**: 记录关键步骤便于调试
3. **用户反馈**: 提供清晰的购买状态反馈
4. **错误恢复**: 实现购买失败后的恢复机制
