# In-App Purchase 配置指南

本应用已经实现了基于 Expo SDK 53 的真实 In-App Purchase 功能。以下是完整的配置和测试指南。

## 📋 功能概述

- ✅ 真实的 In-App Purchase 实现（替换了之前的模拟购买）
- ✅ 支持 iOS 和 Android 平台
- ✅ 自动恢复购买功能
- ✅ 完整的错误处理和用户反馈
- ✅ 购买状态持久化存储

## 🛠 技术实现

### 核心文件
- `app/utils/inAppPurchases.ts` - In-App Purchase 核心逻辑
- `app/screens/profile.tsx` - 购买界面和流程
- `app/utils/testInAppPurchase.ts` - 测试工具

### 产品配置
- **产品ID**: `com.yourcompany.ninecents.premium`
- **类型**: 一次性购买（Non-consumable）
- **功能**: 解锁标签管理等高级功能

## 📱 平台配置

### iOS 配置

1. **App Store Connect 设置**
   ```
   - 登录 App Store Connect
   - 创建应用（如果还没有）
   - 在 "功能" > "App 内购买项目" 中添加产品
   - 产品ID: com.yourcompany.ninecents.premium
   - 类型: 非消耗型
   - 价格: 根据需要设置
   ```

2. **Bundle Identifier**
   ```json
   // app.json
   "ios": {
     "bundleIdentifier": "com.yourcompany.ninecents"
   }
   ```

3. **测试账户**
   ```
   - 在 App Store Connect 中创建沙盒测试账户
   - 在 iOS 设备的设置中登录沙盒账户
   ```

### Android 配置

1. **Google Play Console 设置**
   ```
   - 登录 Google Play Console
   - 在 "货币化" > "产品" > "应用内产品" 中添加产品
   - 产品ID: com.yourcompany.ninecents.premium
   - 类型: 受管理的产品
   ```

2. **Package Name**
   ```json
   // app.json
   "android": {
     "package": "com.yourae.nineCents"
   }
   ```

## 🧪 测试指南

### 开发环境测试

1. **运行测试函数**
   ```typescript
   import { runInAppPurchaseTest } from './app/utils/testInAppPurchase';
   
   // 在开发环境中调用
   runInAppPurchaseTest();
   ```

2. **检查控制台输出**
   ```
   🧪 Testing In-App Purchase Setup...
   ✅ In-App Purchase Available: true
   ✅ Connected to App Store: true
   ✅ Products loaded: 1
   📦 Product Details: {...}
   ```

### 真机测试

1. **iOS 测试**
   ```bash
   # 构建开发版本
   npx expo run:ios
   
   # 或使用 EAS Build
   eas build --platform ios --profile development
   ```

2. **Android 测试**
   ```bash
   # 构建开发版本
   npx expo run:android
   
   # 或使用 EAS Build
   eas build --platform android --profile development
   ```

## 🔧 故障排除

### 常见问题

1. **产品未找到**
   ```
   问题: Products loaded: 0
   解决: 检查产品ID是否正确，确保在应用商店中已配置
   ```

2. **连接失败**
   ```
   问题: Connected to App Store: false
   解决: 检查网络连接，确保设备支持In-App Purchase
   ```

3. **购买失败**
   ```
   问题: Purchase failed
   解决: 检查测试账户，确保产品状态为"准备提交"
   ```

### 调试技巧

1. **启用详细日志**
   ```typescript
   // 在 inAppPurchases.ts 中添加更多日志
   console.log('Purchase initiated:', productId);
   ```

2. **检查购买历史**
   ```typescript
   const history = await InAppPurchases.getPurchaseHistoryAsync();
   console.log('Purchase history:', history);
   ```

## 🚀 发布准备

### 发布前检查清单

- [ ] 产品在 App Store Connect / Google Play Console 中已配置
- [ ] 产品状态为"准备提交"
- [ ] Bundle ID / Package Name 与配置一致
- [ ] 测试账户购买流程正常
- [ ] 恢复购买功能正常

### 发布配置

```json
// app.json
{
  "expo": {
    "plugins": [
      "expo-in-app-purchases"
    ]
  }
}
```

## 📚 相关文档

- [Expo In-App Purchases 文档](https://docs.expo.dev/versions/latest/sdk/in-app-purchases/)
- [App Store Connect 指南](https://developer.apple.com/app-store-connect/)
- [Google Play Console 指南](https://play.google.com/console/)

## 💡 注意事项

1. **沙盒环境**: 开发和测试时使用沙盒环境，不会产生真实费用
2. **产品配置**: 确保产品ID在代码和应用商店中完全一致
3. **测试账户**: 使用专门的测试账户，不要使用个人Apple ID
4. **审核要求**: 确保购买流程符合应用商店审核指南
