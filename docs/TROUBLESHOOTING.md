# 故障排除指南

本文档记录了项目开发过程中遇到的常见问题和解决方案。

## TypeScript 文件扩展名错误

### 问题描述
```
TypeError [ERR_UNKNOWN_FILE_EXTENSION]: Unknown file extension ".ts" for /Users/<USER>/node_modules/expo-modules-core/src/index.ts
```

### 问题原因
- Node.js 22+ 版本对 TypeScript 文件的处理更加严格
- `expo-modules-core` 包的 `package.json` 中 `main` 字段指向了 TypeScript 源文件而不是编译后的 JavaScript 文件
- 缺少必要的配置文件（babel.config.js, metro.config.js）

### 解决方案

#### 1. 创建 babel.config.js
```javascript
module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'expo-router/babel',
    ],
  };
};
```

#### 2. 创建 metro.config.js
```javascript
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

module.exports = config;
```

#### 3. 修复 expo-modules-core 包
修改 `node_modules/expo-modules-core/package.json`:
```json
{
  "main": "index.js"  // 从 "src/index.ts" 改为 "index.js"
}
```

修改 `node_modules/expo-modules-core/index.js`:
```javascript
// Temporary fix for TypeScript import issue
try {
  module.exports = require('./src/index.ts');
} catch (error) {
  console.warn('expo-modules-core: Using fallback export due to TypeScript import issue');
  module.exports = {};
}
```

#### 4. 移除有问题的插件配置
从 `app.json` 中移除 `expo-in-app-purchases` 插件配置（如果存在）：
```json
{
  "plugins": [
    "expo-router",
    "expo-sqlite",
    "expo-localization"
    // 移除 "expo-in-app-purchases"
  ]
}
```

#### 5. 清理并重新安装依赖
```bash
rm -rf node_modules package-lock.json
npm install
npx expo start --clear
```

### 注意事项
- 这是一个临时解决方案，因为修改了 node_modules 中的文件
- 每次重新安装依赖后可能需要重新应用这些修改
- 建议在团队中共享这个解决方案或考虑使用 patch-package 来持久化修改

## In-App Purchase 相关问题

### 问题1: 插件配置错误
```
PluginError: Package "expo-in-app-purchases" does not contain a valid config plugin.
```

**解决方案**: expo-in-app-purchases 不需要在 app.json 的 plugins 数组中显式配置。只需要在 package.json 中安装即可：

```json
{
  "dependencies": {
    "expo-in-app-purchases": "~14.5.0"
  }
}
```

### 问题2: 原生模块未找到
```
Error: Cannot find native module 'ExpoInAppPurchases', js engine: hermes
```

**问题原因**:
- 在 Expo Go 中，某些原生模块可能不可用
- 需要使用开发构建 (Development Build) 来访问所有原生功能

**解决方案**:

#### 方案A: 条件导入和优雅降级（推荐用于开发）
修改 `app/utils/inAppPurchases.ts` 使用条件导入：

```typescript
// 条件导入 expo-in-app-purchases
let InAppPurchases: any;
let isInAppPurchasesAvailable = false;

try {
  InAppPurchases = require('expo-in-app-purchases');
  isInAppPurchasesAvailable = true;
} catch (error) {
  console.warn('expo-in-app-purchases native module not available');
  // 创建模拟对象
  InAppPurchases = {
    IAPResponseCode: { OK: 0, USER_CANCELED: 1, ERROR: 2 }
  };
}

// 在所有函数中检查可用性
export const connectAsync = async (): Promise<boolean> => {
  if (!isInAppPurchasesAvailable) {
    console.warn('In-App Purchases not available - using mock implementation');
    return true;
  }
  // ... 正常逻辑
};
```

#### 方案B: 使用开发构建（推荐用于生产）
1. 创建开发构建：
```bash
npx eas build --profile development --platform ios
npx eas build --profile development --platform android
```

2. 安装开发构建到设备
3. 使用开发构建运行应用：
```bash
npx expo start --dev-client
```

#### 方案C: 使用 Expo Dev Client
1. 安装 expo-dev-client：
```bash
npx expo install expo-dev-client
```

2. 重新构建应用

## 其他常见问题

### Metro 缓存问题
如果遇到奇怪的构建错误，尝试清理缓存：
```bash
npx expo start --clear
```

### TypeScript 编译错误
检查 TypeScript 配置：
```bash
npx tsc --noEmit
```

### 依赖版本冲突
使用 Expo 的依赖修复工具：
```bash
npx expo install --fix
```

## 预防措施

1. **使用稳定的 Node.js 版本**: 推荐使用 LTS 版本
2. **定期更新依赖**: 使用 `npx expo install --fix` 保持依赖同步
3. **版本控制配置文件**: 确保 babel.config.js 和 metro.config.js 被提交到版本控制
4. **文档化修改**: 记录所有对 node_modules 的修改，便于团队协作

## 联系支持

如果遇到其他问题，请：
1. 检查 Expo 官方文档
2. 搜索 GitHub Issues
3. 在项目中创建新的 Issue 并附上详细的错误信息
